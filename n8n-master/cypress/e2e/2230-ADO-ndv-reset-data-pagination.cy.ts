import { NDV, WorkflowPage } from '../pages';
import { clearNotifications } from '../pages/notifications';

const workflowPage = new WorkflowPage();
const ndv = new NDV();

describe('ADO-2230 NDV Pagination Reset', () => {
	it('should reset pagaintion if data size changes to less than current page', () => {
		// setup, load workflow with debughelper node with random seed
		workflowPage.actions.visit();
		cy.createFixtureWorkflow('NDV-debug-generate-data.json', 'Debug workflow');
		workflowPage.actions.openNode('DebugHelper');

		// execute node outputting 10 pages, check output of first page
		ndv.actions.execute();
		clearNotifications();
		ndv.getters.outputTbodyCell(1, 1).invoke('text').should('eq', '<EMAIL>');

		// open 4th page, check output
		ndv.getters.pagination().should('be.visible');
		ndv.getters.pagination().find('li.number').should('have.length', 5);
		ndv.getters.pagination().find('li.number').eq(3).click();
		ndv.getters.outputTbodyCell(1, 1).invoke('text').should('eq', '<EMAIL>');

		// output a lot less data
		ndv.getters.parameterInput('randomDataCount').find('input').clear().type('20');
		ndv.actions.execute();
		clearNotifications();

		// check we are back to second page now
		ndv.getters.pagination().find('li.number').should('have.length', 2);
		ndv.getters.outputTbodyCell(1, 1).invoke('text').should('eq', '<EMAIL>');
	});
});
