var File = function(url, object){
	File.list = Array.isArray(File.list)? File.list : [];
	File.progress = File.progress || 0;
	this.progress = 0;
	this.object = object;
	this.url = url;
};

File.indexOf = function(term){
	for(var index in File.list){
		var file = File.list[index];
		if (file.equals(term) || file.url === term || file.object === term) {
			return index;
		}
	}
	return -1;
};

File.find = function(term){
	var index = File.indexOf(term);
	return ~index && File.list[index];
};

File.prototype.equals = function(file){
	var isFileType = file instanceof File;
	return isFileType && this.url === file.url && this.object === file.object;
};

File.prototype.save = function(update){
	update = typeof update === 'undefined'? true : update;
	if(Array.isArray(File.list)){
		var index = File.indexOf(this);
		if(~index && update) {
			File.list[index] = this;
			console.warn('File `%s` has been loaded before and updated now for: %O.', this.url, this);
		}else File.list.push(this);
		console.log(File.list)
	}else{
		File.list = [this];
	}
	return this;
};
