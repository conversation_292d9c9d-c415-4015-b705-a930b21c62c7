{"meta": {"instanceId": "15bbf37b6a515ccc2f534cabcd8bd171ca33583ff7744b1e9420e5ce68e615bb"}, "nodes": [{"parameters": {}, "id": "40720511-19b6-4421-bdb0-3fb6efef4bc5", "name": "When clicking ‘Execute workflow’", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [280, 320]}, {"parameters": {}, "id": "acdd1bdc-c642-4ea6-ad67-f4201b640cfa", "name": "Unknown node 1", "type": "n8n-nodes-base.thisNodeDoesntExist", "typeVersion": 1, "position": [400, 500]}, {"parameters": {}, "id": "acdd1bdc-c642-4ea6-ad67-f4201b640ffa", "name": "Unknown node 2", "type": "n8n-nodes-base.thisNodeDoesntExistEither", "typeVersion": 1, "position": [600, 500]}, {"parameters": {"options": {}}, "id": "fbe5163b-7474-4741-980a-e4956789be0a", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [500, 320]}, {"parameters": {"options": {}}, "id": "163313b9-64ff-4ffc-b00f-09b267d8132c", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [720, 320]}], "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}}}