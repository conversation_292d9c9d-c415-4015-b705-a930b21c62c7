{"name": "fake_embeddings", "nodes": [{"parameters": {}, "id": "de3c1210-3be7-49a6-86ef-9435e661f23f", "name": "When clicking ‘Execute workflow’", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [480, 760]}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $('Code').item.json.city }}", "options": {}}, "id": "de3cb132-14ef-426b-ad33-8365a93dd11f", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1100, 900]}, {"parameters": {"jsCode": "const kyiv = `Kyiv (also Kiev)[a] is the capital and most populous city of Ukraine. It is in north-central Ukraine along the Dnieper River. As of 1 January 2022, its population was 2,952,301,[2] making Kyiv the seventh-most populous city in Europe.[11] Kyiv is an important industrial, scientific, educational, and cultural center in Eastern Europe. It is home to many high-tech industries, higher education institutions, and historical landmarks. The city has an extensive system of public transport and infrastructure, including the Kyiv Metro.\n\nThe city's name is said to derive from the name of <PERSON><PERSON>, one of its four legendary founders. During its history, Kyiv, one of the oldest cities in Eastern Europe, passed through several stages of prominence and obscurity. The city probably existed as a commercial center as early as the 5th century. A Slavic settlement on the great trade route between Scandinavia and Constantinople, Kyiv was a tributary of the Khazars,[12] until its capture by the Varangians (Vikings) in the mid-9th century. Under Varangian rule, the city became a capital of Kievan Rus', the first East Slavic state. Completely destroyed during the Mongol invasions in 1240, the city lost most of its influence for the centuries to come. Coming under Lithuania, then Poland and then Russia, the city would grow from a frontier market into an important centre of Orthodox learning in the sixteenth century, and later of industry, commerce, and administration by the nineteenth.[1]\n\nThe city prospered again during the Russian Empire's Industrial Revolution in the late 19th century. In 1918, when the Ukrainian People's Republic declared independence from the Russian Republic after the October Revolution there, Kyiv became its capital. From the end of the Ukrainian-Soviet and Polish-Soviet wars in 1921, Kyiv was a city of the Ukrainian SSR, and made its capital in 1934. The city suffered significant destruction during World War II but quickly recovered in the postwar years, remaining the Soviet Union's third-largest city.\n\nFollowing the collapse of the Soviet Union and Ukrainian independence in 1991, Kyiv remained Ukraine's capital and experienced a steady influx of ethnic Ukrainian migrants from other regions of the country.[13] During the country's transformation to a market economy and electoral democracy, Kyiv has continued to be Ukraine's largest and wealthiest city. Its armament-dependent industrial output fell after the Soviet collapse, adversely affecting science and technology, but new sectors of the economy such as services and finance facilitated Kyiv's growth in salaries and investment, as well as providing continuous funding for the development of housing and urban infrastructure. Kyiv emerged as the most pro-Western region of Ukraine; parties advocating tighter integration with the European Union dominate during elections.`\n\nconst berlin = `Berlin[a] is the capital and largest city of Germany, both by area and by population.[11] Its more than 3.85 million inhabitants[12] make it the European Union's most populous city, as measured by population within city limits.[13] The city is also one of the states of Germany, and is the third smallest state in the country in terms of area. Berlin is surrounded by the state of Brandenburg, and Brandenburg's capital Potsdam is nearby. The urban area of Berlin has a population of over 4.5 million and is therefore the most populous urban area in Germany.[5][14] The Berlin-Brandenburg capital region has around 6.2 million inhabitants and is Germany's second-largest metropolitan region after the Rhine-Ruhr region, and the sixth-biggest metropolitan region by GDP in the European Union.[15]\n\nBerlin was built along the banks of the Spree river, which flows into the Havel in the western borough of Spandau. The city incorporates lakes in the western and southeastern boroughs, the largest of which is Müggelsee. About one-third of the city's area is composed of forests, parks and gardens, rivers, canals, and lakes.[16]\n\nFirst documented in the 13th century[10] and at the crossing of two important historic trade routes,[17] Berlin was designated the capital of the Margraviate of Brandenburg (1417–1701), Kingdom of Prussia (1701–1918), German Empire (1871–1918), Weimar Republic (1919–1933), and Nazi Germany (1933–1945). Berlin has served as a scientific, artistic, and philosophical hub during the Age of Enlightenment, Neoclassicism, and the German revolutions of 1848–1849. During the Gründerzeit, an industrialization-induced economic boom triggered a rapid population increase in Berlin. 1920s Berlin was the third-largest city in the world by population.[18]\n\nAfter World War II and following Berlin's occupation, the city was split into West Berlin and East Berlin, divided by the Berlin Wall.[19] East Berlin was declared the capital of East Germany, while Bonn became the West German capital. Following German reunification in 1990, Berlin once again became the capital of all of Germany. Due to its geographic location and history, Berlin has been called \"the heart of Europe\".[20][21][22]`\n\nconst prague = `Prague (/ˈprɑːɡ/ PRAHG; Czech: Praha [ˈpraɦa] ⓘ)[a] is the capital and largest city of the Czech Republic[9] and the historical capital of Bohemia. Situated on the Vltava river, Prague is home to about 1.4 million people.\n\nPrague is a political, cultural, and economic hub of Central Europe, with a rich history and Romanesque, Gothic, Renaissance and Baroque architectures. It was the capital of the Kingdom of Bohemia and residence of several Holy Roman Emperors, most notably Charles IV (r. 1346–1378) and Rudolf II (r. 1575–1611).[9] It was an important city to the Habsburg monarchy and Austria-Hungary. The city played major roles in the Bohemian and the Protestant Reformations, the Thirty Years' War and in 20th-century history as the capital of Czechoslovakia between the World Wars and the post-war Communist era.[10]\n\nPrague is home to a number of cultural attractions including Prague Castle, Charles Bridge, Old Town Square with the Prague astronomical clock, the Jewish Quarter, Petřín hill and Vyšehrad. Since 1992, the historic center of Prague has been included in the UNESCO list of World Heritage Sites.\n\nThe city has more than ten major museums, along with numerous theatres, galleries, cinemas, and other historical exhibits. An extensive modern public transportation system connects the city. It is home to a wide range of public and private schools, including Charles University in Prague, the oldest university in Central Europe.\n\nPrague is classified as an \"Alpha-\" global city according to GaWC studies.[11] In 2019, the city was ranked as 69th most livable city in the world by Mercer.[12] In the same year, the PICSA Index ranked the city as 13th most livable city in the world.[13] Its rich history makes it a popular tourist destination and as of 2017, the city receives more than 8.5 million international visitors annually. In 2017, Prague was listed as the fifth most visited European city after London, Paris, Rome, and Istanbul.[14]`\n\nreturn [prague, berlin, kyiv].map(i => ({ city: i}))"}, "id": "ce9d517e-2dd9-45e4-a566-79bd79cd809b", "name": "Code", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [740, 760]}, {"parameters": {"chunkSize": 300}, "id": "ebe5f3a5-4d90-4a33-bf48-f160f0e83967", "name": "Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [1100, 1060]}, {"parameters": {"code": {"supplyData": {"code": "const { FakeEmbeddings } = require('@langchain/core/utils/testing');\n\nreturn new FakeEmbeddings();"}}, "outputs": {"output": [{"type": "ai_embedding"}]}}, "id": "0eac6c5b-89a9-48a4-bd21-19f2b20c3424", "name": "Fake Embeddings 3", "type": "@n8n/n8n-nodes-langchain.code", "typeVersion": 1, "position": [660, 1220]}, {"parameters": {"mode": "load", "prompt": "Tester", "topK": 3}, "id": "8c9b39bf-59d6-4769-98e1-54988d9d6b53", "name": "Get All VS", "type": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "typeVersion": 1, "position": [680, 1080]}, {"parameters": {"code": {"supplyData": {"code": "const { FakeEmbeddings } = require('@langchain/core/utils/testing');\n\nreturn new FakeEmbeddings();"}}, "outputs": {"output": [{"type": "ai_embedding"}]}}, "id": "e46004ec-baf6-425c-9897-3faec9e29676", "name": "Fake Embeddings", "type": "@n8n/n8n-nodes-langchain.code", "typeVersion": 1, "position": [920, 900]}, {"parameters": {"promptType": "define", "text": "Just testing", "options": {}}, "id": "b132b323-a813-469c-859b-f1b3ede743a3", "name": "Question and Answer Chain", "type": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "typeVersion": 1.3, "position": [1680, 780]}, {"parameters": {}, "id": "b9c412e5-d739-4c82-9a2e-6c0af0cae8f9", "name": "Vector Store Retriever", "type": "@n8n/n8n-nodes-langchain.retrieverVectorStore", "typeVersion": 1, "position": [1760, 920]}, {"parameters": {"code": {"supplyData": {"code": "const { FakeChatModel } = require('@langchain/core/utils/testing');\n\nreturn new FakeChatModel({});"}}, "outputs": {"output": [{"type": "ai_languageModel"}]}}, "id": "962b4b87-ffd6-4ab8-8776-6e9c0920930a", "name": "Fake Language Model", "type": "@n8n/n8n-nodes-langchain.code", "typeVersion": 1, "position": [1620, 920]}, {"parameters": {"code": {"supplyData": {"code": "const { FakeEmbeddings } = require('@langchain/core/utils/testing');\n\nreturn new FakeEmbeddings();"}}, "outputs": {"output": [{"type": "ai_embedding"}]}}, "id": "c78be34f-6459-4414-86bd-f2670ece129d", "name": "Fake Embeddings 2", "type": "@n8n/n8n-nodes-langchain.code", "typeVersion": 1, "position": [1700, 1200]}, {"parameters": {}, "id": "3cee9727-6b97-477c-8277-e8883a98786d", "name": "Retriever VS", "type": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "typeVersion": 1, "position": [1700, 1060]}, {"parameters": {"mode": "insert"}, "id": "5793ec6b-ac00-4a5d-a79c-ff557143e46b", "name": "Populate VS", "type": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "typeVersion": 1, "position": [980, 760]}], "pinData": {}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Code", "type": "main", "index": 0}, {"node": "Get All VS", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Populate VS", "type": "ai_document", "index": 0}]]}, "Code": {"main": [[{"node": "Populate VS", "type": "main", "index": 0}]]}, "Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Fake Embeddings 3": {"ai_embedding": [[{"node": "Get All VS", "type": "ai_embedding", "index": 0}]]}, "Fake Embeddings": {"ai_embedding": [[{"node": "Populate VS", "type": "ai_embedding", "index": 0}]]}, "Vector Store Retriever": {"ai_retriever": [[{"node": "Question and Answer Chain", "type": "ai_retriever", "index": 0}]]}, "Fake Language Model": {"ai_languageModel": [[{"node": "Question and Answer Chain", "type": "ai_languageModel", "index": 0}]]}, "Fake Embeddings 2": {"ai_embedding": [[{"node": "Retriever VS", "type": "ai_embedding", "index": 0}]]}, "Retriever VS": {"ai_vectorStore": [[{"node": "Vector Store Retriever", "type": "ai_vectorStore", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "4ad44cc6-d5f7-48af-8455-c3957baba04c", "meta": {"templateCredsSetupCompleted": true, "instanceId": "27cc9b56542ad45b38725555722c50a1c3fee1670bbb67980558314ee08517c4"}, "id": "ZjxsuN0rMHRVCb2c", "tags": []}