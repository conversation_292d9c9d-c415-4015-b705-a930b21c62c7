{"name": "n8n-cypress", "private": true, "scripts": {"typecheck": "tsc --noEmit", "cypress:install": "cypress install", "test:e2e:ui": "scripts/run-e2e.js ui", "test:e2e:dev": "scripts/run-e2e.js dev", "test:e2e:all": "scripts/run-e2e.js all", "test:flaky": "scripts/run-e2e.js debugFlaky", "format": "biome format --write .", "format:check": "biome ci .", "lint": "eslint . --quiet", "lintfix": "eslint . --fix", "develop": "cd ..; pnpm dev:e2e:server", "start": "cd ..; pnpm start"}, "devDependencies": {"@cypress/grep": "^4.1.0", "@n8n/api-types": "workspace:*", "@types/lodash": "catalog:", "eslint-plugin-cypress": "^3.5.0", "n8n-workflow": "workspace:*"}, "dependencies": {"@ngneat/falso": "^7.3.0", "@sinonjs/fake-timers": "^13.0.2", "cypress": "^14.4.0", "cypress-otp": "^1.0.3", "cypress-real-events": "^1.14.0", "flatted": "catalog:", "lodash": "catalog:", "nanoid": "catalog:", "start-server-and-test": "^2.0.10"}}