{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "editor.codeActionsOnSave": {"quickfix.biome": "explicit", "source.organizeImports.biome": "never"}, "search.exclude": {"node_modules": true, "dist": true, "pnpm-lock.yaml": true, "**/*.snapshot.json": true, "test-workflows": true}, "typescript.format.enable": false, "typescript.tsdk": "node_modules/typescript/lib", "workspace-default-settings.runOnActivation": true, "prettier.prettierPath": "node_modules/prettier/index.cjs", "eslint.probe": ["javascript", "typescript", "vue"], "eslint.workingDirectories": [{"mode": "auto"}]}