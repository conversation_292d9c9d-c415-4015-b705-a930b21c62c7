name: 'Setup Environment and Build Project'
description: 'Sets up Node.js with pnpm, installs dependencies, (optional) enables Turborepo caching, and (optional) builds the project.'

inputs:
  node-version:
    description: 'Node.js version to use.'
    required: false
    default: '22.x'
  enable-caching:
    description: 'Flag to enable/disable all caching.'
    required: false
    default: 'true'
  cache-suffix:
    description: 'Suffix to add to the cache key.'
    required: false
    default: 'base:build'
  skip-build:
    description: 'Skip the build step, useful when restoring cached artifacts.'
    required: false
    default: 'false'
  cache-paths:
    description: 'Paths to cache. Defaults to dist folders.'
    required: false
    default: './packages/**/dist'

runs:
  using: 'composite'
  steps:
    - name: Setup pnpm CLI
      uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
      with:
        run_install: false

    - name: Setup Node.js
      uses: useblacksmith/setup-node@v5
      with:
        node-version: ${{ inputs.node-version }}
        cache: pnpm

    - name: Install dependencies
      run: pnpm install --frozen-lockfile
      shell: bash

    - name: Configure Turborepo Cache
      if: inputs.enable-caching == 'true'
      uses: useblacksmith/caching-for-turbo@v1

    - name: Build packages
      if: inputs.skip-build == 'false'
      run: pnpm build
      shell: bash

    - name: Cache artifacts
      if: inputs.enable-caching == 'true' && inputs.skip-build == 'false'
      uses: useblacksmith/cache@v5
      with:
        path: ${{ inputs.cache-paths }}
        key: ${{ github.sha }}-${{ inputs.cache-suffix }}
