name: Debug Flaky E2E Test

on:
  workflow_dispatch:
    inputs:
      test_name:
        description: 'The name of the test to filter.'
        required: true
        type: string
      burn_count:
        description: 'Number of times to run the test.'
        required: false
        type: number
        default: 50
      branch:
        description: 'Optional: GitHub branch, tag, or SHA to test. Defaults to the branch selected in UI.'
        required: false
        type: string

jobs:
  debug-test:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    timeout-minutes: 60

    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Setup PNPM
        uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0

      - name: Setup Node.js
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 22.x
          cache: 'pnpm'

      - name: Cache build artifacts
        id: cache-build-artifacts
        uses: useblacksmith/cache@c5fe29eb0efdf1cf4186b9f7fcbbcbc0cf025662 # v5.0.2
        with:
          path: |
            /home/<USER>/.cache/Cypress
            ./packages/**/dist
          key: ${{ github.ref }}-${{ github.sha }}-debug-build
          restore-keys: |
            ${{ github.ref }}-debug-build-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build application
        if: steps.cache-build-artifacts.outputs.cache-hit != 'true'
        run: pnpm build

      - name: Cypress install
        if: steps.cache-build-artifacts.outputs.cache-hit != 'true'
        working-directory: cypress
        run: pnpm cypress:install

      - name: Run Flaky Debug Command
        run: pnpm run debug:flaky:e2e "${{ github.event.inputs.test_name }}" ${{ github.event.inputs.burn_count }}
        env:
          NODE_OPTIONS: --dns-result-order=ipv4first
          E2E_TESTS: true
          SHELL: /bin/sh
