name: Docker Custom Image CI
run-name: Build ${{ inputs.branch }} - ${{ inputs.user }}

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'GitHub branch to create image off.'
        required: true
      tag:
        description: 'Name of the docker tag to create.'
        required: true
      merge-master:
        description: 'Merge with master.'
        type: boolean
        required: true
        default: false
      user:
        description: ''
        required: false
        default: 'none'
      start-url:
        description: 'URL to call after workflow is kicked off.'
        required: false
        default: ''
      success-url:
        description: 'URL to call after Docker Image got built successfully.'
        required: false
        default: ''

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Call Start URL - optionally
        if: ${{ github.event.inputs.start-url != '' }}
        run: curl -v -X POST -d 'url=${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}' ${{github.event.inputs.start-url}} || echo ""
        shell: bash

      - name: Checkout
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Merge Master - optionally
        if: github.event.inputs.merge-master
        run: git remote add upstream https://github.com/n8n-io/n8n.git -f; git merge upstream/master --allow-unrelated-histories || echo ""
        shell: bash

      - name: Set up QEMU
        uses: docker/setup-qemu-action@53851d14592bedcffcf25ea515637cff71ef929a # v3.3.0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@6524bf65af31da8d45b59e8c27de4bd072b392f5 # v3.8.0

      - name: Login to GHCR
        uses: docker/login-action@9780b0c442fbb1117ed29e0efdff1e18412f7567 # v3.3.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push image to GHCR
        uses: docker/build-push-action@b32b51a8eda65d6793cd0494a773d4f6bcef32dc # v6.11.0
        env:
          DOCKER_BUILD_SUMMARY: false
        with:
          context: .
          file: ./docker/images/n8n/Dockerfile
          build-args: |
            N8N_RELEASE_TYPE=development
          platforms: linux/amd64
          provenance: false
          push: true
          cache-from: type=gha
          cache-to: type=gha,mode=max
          tags: ghcr.io/${{ github.repository_owner }}/n8n:${{ inputs.tag }}

      - name: Call Success URL - optionally
        if: ${{ github.event.inputs.success-url != '' }}
        run: curl -v ${{github.event.inputs.success-url}} || echo ""
        shell: bash
