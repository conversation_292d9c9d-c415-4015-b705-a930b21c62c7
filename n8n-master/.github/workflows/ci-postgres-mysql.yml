name: Test Postgres and MySQL schemas

on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:
  pull_request:
    paths:
      - packages/cli/src/databases/**
      - packages/@n8n/db/**
      - packages/cli/src/modules/*/database/**
      - packages/cli/test/integration/**
      - packages/cli/test/shared/db/**
      - packages/@n8n/db/**
      - .github/workflows/ci-postgres-mysql.yml
      - .github/docker-compose.yml
  pull_request_review:
    types: [submitted]

concurrency:
  group: db-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: false

jobs:
  build:
    name: Install & Build
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request_review' || startsWith(github.event.pull_request.base.ref, 'release/')
    steps:
      - uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1

      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 22.x

      - name: Setup corepack and pnpm
        run: |
          npm i -g corepack@0.33
          corepack enable

      - run: pnpm install --frozen-lockfile

      - name: Setup build cache
        uses: rharkor/caching-for-turbo@439abec0d28d21b192fa8817b744ffdf1ee5ac0d # v1.5

      - name: Build
        run: pnpm build

      - name: Cache build artifacts
        uses: actions/cache/save@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        with:
          path: ./packages/**/dist
          key: ${{ github.sha }}:db-tests

  sqlite-pooled:
    name: SQLite Pooled
    runs-on: ubuntu-latest
    needs: build
    timeout-minutes: 20
    env:
      DB_TYPE: sqlite
      DB_SQLITE_POOL_SIZE: 4
    steps:
      - uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1

      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 22.x

      - name: Setup corepack and pnpm
        run: |
          npm i -g corepack@0.33
          corepack enable

      - run: pnpm install --frozen-lockfile

      - name: Setup build cache
        uses: rharkor/caching-for-turbo@439abec0d28d21b192fa8817b744ffdf1ee5ac0d # v1.5

      - name: Restore cached build artifacts
        uses: actions/cache/restore@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        with:
          path: ./packages/**/dist
          key: ${{ github.sha }}:db-tests

      - name: Test SQLite Pooled
        working-directory: packages/cli
        run: pnpm jest

  mariadb:
    name: MariaDB
    runs-on: ubuntu-latest
    needs: build
    timeout-minutes: 20
    env:
      DB_MYSQLDB_PASSWORD: password
    steps:
      - uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1

      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 22.x

      - name: Setup corepack and pnpm
        run: |
          npm i -g corepack@0.33
          corepack enable

      - run: pnpm install --frozen-lockfile

      - name: Setup build cache
        uses: rharkor/caching-for-turbo@439abec0d28d21b192fa8817b744ffdf1ee5ac0d # v1.5

      - name: Restore cached build artifacts
        uses: actions/cache/restore@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        with:
          path: ./packages/**/dist
          key: ${{ github.sha }}:db-tests

      - name: Start MariaDB
        uses: isbang/compose-action@802a148945af6399a338c7906c267331b39a71af # v2.0.0
        with:
          compose-file: ./.github/docker-compose.yml
          services: |
            mariadb

      - name: Test MariaDB
        working-directory: packages/cli
        run: pnpm test:mariadb --testTimeout 60000

  mysql:
    name: MySQL (${{ matrix.service-name }})
    runs-on: ubuntu-latest
    needs: build
    timeout-minutes: 20
    strategy:
      matrix:
        service-name: ['mysql-8.0.13', 'mysql-8.4']
    env:
      DB_MYSQLDB_PASSWORD: password
    steps:
      - uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1

      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 22.x

      - name: Setup corepack and pnpm
        run: |
          npm i -g corepack@0.33
          corepack enable

      - run: pnpm install --frozen-lockfile

      - name: Setup build cache
        uses: rharkor/caching-for-turbo@439abec0d28d21b192fa8817b744ffdf1ee5ac0d # v1.5

      - name: Restore cached build artifacts
        uses: actions/cache/restore@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        with:
          path: ./packages/**/dist
          key: ${{ github.sha }}:db-tests

      - name: Start MySQL
        uses: isbang/compose-action@802a148945af6399a338c7906c267331b39a71af # v2.0.0
        with:
          compose-file: ./.github/docker-compose.yml
          services: |
            ${{ matrix.service-name }}

      - name: Test MySQL
        working-directory: packages/cli
        run: pnpm test:mysql --testTimeout 60000

  postgres:
    name: Postgres
    runs-on: ubuntu-latest
    needs: build
    timeout-minutes: 20
    env:
      DB_POSTGRESDB_PASSWORD: password
      DB_POSTGRESDB_POOL_SIZE: 1 # Detect connection pooling deadlocks
    steps:
      - uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1

      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 22.x

      - name: Setup corepack and pnpm
        run: |
          npm i -g corepack@0.33
          corepack enable

      - run: pnpm install --frozen-lockfile

      - name: Setup build cache
        uses: rharkor/caching-for-turbo@439abec0d28d21b192fa8817b744ffdf1ee5ac0d # v1.5

      - name: Restore cached build artifacts
        uses: actions/cache/restore@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        with:
          path: ./packages/**/dist
          key: ${{ github.sha }}:db-tests

      - name: Start Postgres
        uses: isbang/compose-action@802a148945af6399a338c7906c267331b39a71af # v2.0.0
        with:
          compose-file: ./.github/docker-compose.yml
          services: |
            postgres

      - name: Test Postgres
        working-directory: packages/cli
        run: pnpm test:postgres

  notify-on-failure:
    name: Notify Slack on failure
    runs-on: ubuntu-latest
    needs: [mariadb, postgres, mysql]
    steps:
      - name: Notify Slack on failure
        uses: act10ns/slack@44541246747a30eb3102d87f7a4cc5471b0ffb7d # v2.1.0
        if: failure() && github.ref == 'refs/heads/master'
        with:
          status: ${{ job.status }}
          channel: '#alerts-build'
          webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
          message: Postgres, MariaDB or MySQL tests failed (${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
