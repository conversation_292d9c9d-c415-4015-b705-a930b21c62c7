name: Reusable e2e workflow

on:
  workflow_call:
    inputs:
      branch:
        description: 'GitHub branch to test.'
        required: false
        type: string
      user:
        description: 'User who kicked this off.'
        required: false
        type: string
        default: 'schedule'
      spec:
        description: 'Specify specs.'
        required: false
        default: 'e2e/*'
        type: string
      record:
        description: 'Record test run.'
        required: false
        default: true
        type: boolean
      parallel:
        description: 'Run tests in parallel.'
        required: false
        default: true
        type: boolean
      containers:
        description: 'Number of containers to run tests in.'
        required: false
        default: '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]'
        type: string
      pr_number:
        description: 'PR number to run tests for.'
        required: false
        type: number
    secrets:
      CYPRESS_RECORD_KEY:
        description: 'Cypress record key.'
        required: true
    outputs:
      tests_passed:
        description: 'True if all E2E tests passed, otherwise false'
        value: ${{ jobs.check_testing_matrix.outputs.all_tests_passed }}

jobs:
  # single job that generates and outputs a common id
  prepare:
    runs-on: ubuntu-latest
    outputs:
      uuid: ${{ steps.uuid.outputs.value }}
    steps:
      - name: Generate unique ID 💎
        id: uuid
        # take the current commit + timestamp together
        # the typical value would be something like
        # "sha-5d3fe...35d3-time-1620841214"
        run: echo "value=sha-$GITHUB_SHA-time-$(date +"%s")" >> $GITHUB_OUTPUT

      - name: Calculate Git Ref 🤔
        id: calculate_ref
        run: |
          if [ -n "${{ inputs.pr_number }}" ]; then
            echo "value=refs/pull/${{ inputs.pr_number }}/head" >> $GITHUB_OUTPUT
          else
            echo "value=${{ inputs.branch }}" >> $GITHUB_OUTPUT
          fi
  install:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    needs: ['prepare']

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ steps.calculate_ref.outputs.value }}

      - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.0.0

      - name: Setup Node.js
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 22.x
          cache: 'pnpm'

      - name: Cache build artifacts
        id: cache-build-artifacts
        uses: useblacksmith/cache@c5fe29eb0efdf1cf4186b9f7fcbbcbc0cf025662 # v5
        with:
          path: |
            /home/<USER>/.cache/Cypress
            /github/home/<USER>
            ./packages/**/dist
          key: ${{ github.sha }}-ui

      - name: Install dependencies
        if: steps.cache-build-artifacts.outputs.cache-hit != 'true'
        run: pnpm install --frozen-lockfile

      - name: Cypress build
        if: steps.cache-build-artifacts.outputs.cache-hit != 'true'
        uses: cypress-io/github-action@be1bab96b388bbd9ce3887e397d373c8557e15af # v6.9.2
        with:
          # Disable running of tests within install job
          runTests: false
          install: false
          build: pnpm build

      - name: Cypress install
        if: steps.cache-build-artifacts.outputs.cache-hit != 'true'
        working-directory: cypress
        run: pnpm cypress:install

  testing:
    runs-on: blacksmith-2vcpu-ubuntu-2204
    needs: ['prepare', 'install']
    strategy:
      fail-fast: false
      matrix:
        # If spec is not e2e/* then we run only one container to prevent
        # running the same tests multiple times
        containers: ${{ fromJSON( inputs.spec == 'e2e/*' && inputs.containers || '[1]' ) }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ steps.calculate_ref.outputs.value }}

      - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.0.0

      - name: Setup Node.js
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 22.x
          cache: 'pnpm'

      - name: Restore cached pnpm modules
        id: cache-build-artifacts
        uses: useblacksmith/cache@c5fe29eb0efdf1cf4186b9f7fcbbcbc0cf025662 # v5
        with:
          path: |
            /home/<USER>/.cache/Cypress
            /github/home/<USER>
            ./packages/**/dist
          key: ${{ github.sha }}-ui

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Cypress run
        uses: cypress-io/github-action@be1bab96b388bbd9ce3887e397d373c8557e15af # v6.9.2
        with:
          working-directory: cypress
          install: false
          start: pnpm start
          wait-on: 'http://localhost:5678'
          wait-on-timeout: 120
          record: ${{ inputs.record }}
          parallel: ${{ fromJSON( inputs.spec == 'e2e/*' && inputs.parallel || false ) }}
          # We have to provide custom ci-build-id key to make sure that this workflow could be run multiple times
          # in the same parent workflow
          ci-build-id: ${{ needs.prepare.outputs.uuid }}
          spec: '${{ inputs.spec }}'
        env:
          NODE_OPTIONS: --dns-result-order=ipv4first
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          E2E_TESTS: true
          COMMIT_INFO_MESSAGE: 🌳 ${{ inputs.branch }} 🤖 ${{ inputs.user }} 🗃️ ${{ inputs.spec }}
          SHELL: /bin/sh

  # Check if all tests passed and set the output variable
  check_testing_matrix:
    runs-on: ubuntu-latest
    needs: [testing]
    outputs:
      all_tests_passed: ${{ steps.all_tests_passed.outputs.result }}
    steps:
      - name: Check all tests passed
        id: all_tests_passed
        run: |
          success=true
          for status in ${{ needs.testing.result }}; do
            if [ $status != "success" ]; then
              success=false
              break
            fi
          done
          echo "::set-output name=result::$success"
