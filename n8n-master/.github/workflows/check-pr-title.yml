name: Check PR title

on:
  pull_request:
    types:
      - opened
      - edited
      - synchronize
    branches:
      - 'master'

jobs:
  check-pr-title:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - name: Check out branch
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1

      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 22.x

      - name: Setup corepack and pnpm
        run: |
          npm i -g corepack@0.33
          corepack enable

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Validate PR title
        id: validate_pr_title
        uses: n8n-io/validate-n8n-pull-request-title@c97ff722ac14ee0bda73766473bba764445db805 # v2.2.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
