name: Docker Nightly Image CI

on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1

      - name: Set up QEMU
        uses: docker/setup-qemu-action@53851d14592bedcffcf25ea515637cff71ef929a # v3.3.0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@6524bf65af31da8d45b59e8c27de4bd072b392f5 # v3.8.0

      - name: Login to GHC<PERSON>
        uses: docker/login-action@9780b0c442fbb1117ed29e0efdff1e18412f7567 # v3.3.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Login to DockerHub
        uses: docker/login-action@9780b0c442fbb1117ed29e0efdff1e18412f7567 # v3.3.0
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push image to GHCR and DockerHub
        uses: docker/build-push-action@b32b51a8eda65d6793cd0494a773d4f6bcef32dc # v6.11.0
        env:
          DOCKER_BUILD_SUMMARY: false
        with:
          context: .
          file: ./docker/images/n8n/Dockerfile
          build-args: |
            N8N_RELEASE_TYPE=nightly
          platforms: linux/amd64,linux/arm64
          provenance: false
          push: true
          cache-from: type=gha
          cache-to: type=gha,mode=max
          tags: |
            ghcr.io/${{ github.repository_owner }}/n8n:nightly
            ${{ secrets.DOCKER_USERNAME }}/n8n:nightly

  security-scan:
    needs: build
    uses: ./.github/workflows/security-trivy-scan-callable.yml
    with:
      image_ref: ghcr.io/${{ github.repository_owner }}/n8n:nightly
    secrets:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
