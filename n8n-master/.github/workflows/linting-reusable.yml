name: Reusable linting workflow

on:
  workflow_call:
    inputs:
      ref:
        description: GitHub ref to lint.
        required: false
        type: string
        default: master
      nodeVersion:
        description: Version of node to use.
        required: false
        type: string
        default: 22.x
      cacheKey:
        description: Cache key for modules and build artifacts.
        required: false
        type: string
        default: ''

jobs:
  lint:
    name: Lint
    runs-on: blacksmith-4vcpu-ubuntu-2204
    steps:
      - uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          ref: ${{ inputs.ref }}

      - name: Setup Environment
        uses: ./.github/actions/setup-and-build
        with:
          node-version: ${{ inputs.nodeVersion }}
          enable-caching: true
          skip-build: ${{ inputs.cacheKey != '' }}

      - name: Lint Backend
        run: pnpm lint:backend

      - name: Lint Nodes
        run: pnpm lint:nodes

      - name: Lint Frontend
        run: pnpm lint:frontend
