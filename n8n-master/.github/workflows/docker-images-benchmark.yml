name: Benchmark Docker Image CI

on:
  workflow_dispatch:
  push:
    branches:
      - master
    paths:
      - 'packages/@n8n/benchmark/**'
      - 'pnpm-lock.yaml'
      - 'pnpm-workspace.yaml'
      - '.github/workflows/docker-images-benchmark.yml'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1

      - name: Set up QEMU
        uses: docker/setup-qemu-action@53851d14592bedcffcf25ea515637cff71ef929a # v3.3.0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@6524bf65af31da8d45b59e8c27de4bd072b392f5 # v3.8.0

      - name: Login to GitHub Container Registry
        uses: docker/login-action@9780b0c442fbb1117ed29e0efdff1e18412f7567 # v3.3.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build
        uses: docker/build-push-action@b32b51a8eda65d6793cd0494a773d4f6bcef32dc # v6.11.0
        env:
          DOCKER_BUILD_SUMMARY: false
        with:
          context: .
          file: ./packages/@n8n/benchmark/Dockerfile
          platforms: linux/amd64
          provenance: false
          push: true
          tags: |
            ghcr.io/${{ github.repository_owner }}/n8n-benchmark:latest
