{"name": "n8n", "dockerComposeFile": "docker-compose.yml", "service": "n8n", "workspaceFolder": "/workspaces", "mounts": ["type=bind,source=${localWorkspaceFolder},target=/workspaces,consistency=cached", "type=bind,source=${localEnv:HOME}/.ssh,target=/home/<USER>/.ssh,consistency=cached", "type=bind,source=${localEnv:HOME}/.n8n,target=/home/<USER>/.n8n,consistency=cached"], "forwardPorts": [8080, 5678], "postCreateCommand": "corepack prepare --activate && pnpm install", "postAttachCommand": "pnpm build", "customizations": {"codespaces": {"openFiles": ["CONTRIBUTING.md"]}}}