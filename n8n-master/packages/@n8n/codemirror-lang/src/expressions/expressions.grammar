@top Program { entity* }

entity { Plaintext | Resolvable }

@tokens {
  Plaintext { ![{] Plaintext? | "{" (@eof | ![{] Plaintext?) }

  OpenMarker[closedBy="CloseMarker"] { "{{" }

  CloseMarker[openedBy="OpenMarker"] { "}}" }

  Resolvable {
    OpenMarker resolvableChar* CloseMarker
  }

  resolvableChar { unicodeChar | "}" ![}] | "\\}}" }

  unicodeChar { $[\u0000-\u007C] | $[\u007E-\u20CF] | $[\u{1F300}-\u{1FAF8}] | $[\u4E00-\u9FFF] }
}

@detectDelim
