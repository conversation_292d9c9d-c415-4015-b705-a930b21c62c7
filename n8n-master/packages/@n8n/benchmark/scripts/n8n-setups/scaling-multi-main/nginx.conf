events {}

http {
		client_max_body_size 50M;
		access_log off;
		error_log /dev/stderr warn;

    upstream backend {
        server n8n_main1:5678;
        server n8n_main2:5678;
    }

    server {
        listen 80;

        location / {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
